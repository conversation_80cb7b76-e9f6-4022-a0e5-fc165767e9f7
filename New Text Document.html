<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <title>Logo Reassembly</title>
  <style>
    body {
      margin: 0;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #f0f0f0;
    }

    .logo-container {
      position: relative;
      width: 800px;
      height: 800px;
      background: white;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
    }

    .logo-part {
      position: absolute;
    }
  </style>
</head>
<body>

  <div id="logo" class="logo-container"></div>

  <script>
    // ترتيب القطع حسب الشكل في الصورة المرفقة
    const parts = [
      // الجزء العلوي الأيسر الكبير (برتقالي)
      { id: 'p05', file: '05.png', top: 50, left: 50, width: 350, height: 200 },
      
      // الجزء العلوي الأيمن (أخضر مثلث)
      { id: 'p04', file: '04.png', top: 50, left: 450, width: 250, height: 200 },
      
      // الجزء الأيسر الطويل (برتقالي)
      { id: 'p06', file: '06.png', top: 300, left: 50, width: 150, height: 300 },
      
      // الجزء الأوسط (برتقالي)
      { id: 'p02', file: '02.png', top: 300, left: 250, width: 150, height: 150 },
      
      // الجزء الأيمن الطويل (أخضر)
      { id: 'p03', file: '03.png', top: 300, left: 450, width: 250, height: 300 },
      
      // الجزء السفلي الأيسر (أخضر مثلث)
      { id: 'p01', file: '01.png', top: 500, left: 50, width: 150, height: 150 },
      
      // الجزء السفلي الأوسط (أخضر)
      { id: 'p07', file: '07.png', top: 500, left: 250, width: 150, height: 150 }
    ];

    const container = document.getElementById('logo');

    parts.forEach(p => {
      const img = document.createElement('img');
      img.src = `partsOfLogo/${p.file}`;
      img.id = p.id;
      img.className = 'logo-part';
      img.style.top = `${p.top}px`;
      img.style.left = `${p.left}px`;
      img.style.width = `${p.width}px`;
      img.style.height = `${p.height}px`;
      container.appendChild(img);
    });
  </script>

</body>
</html>
