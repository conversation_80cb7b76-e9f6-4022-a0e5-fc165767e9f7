<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <title>Logo Reassembly</title>
  <style>
    body {
      margin: 0;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #f0f0f0;
    }

    .logo-container {
      position: relative;
      width: 380px;
      height: 450px;
      background: white;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
      border-radius: 10px;
    }

    .logo-part {
      position: absolute;
      border-radius: 8px;
      object-fit: contain;
    }
  </style>
</head>
<body>

  <div id="logo" class="logo-container"></div>

  <script>
    // ترتيب القطع حسب الصورة المرجعية - تحسين المساحات والمواضع
    const parts = [
      // القطعة 1 - مثلث أخضر سفلي أيسر
      { id: 'p01', file: '01.png', top: 300, left: 20, width: 80, height: 80 },

      // القطعة 2 - مستطيل برتقالي وسط
      { id: 'p02', file: '02.png', top: 170, left: 120, width: 80, height: 80 },

      // القطعة 3 - مستطيل أخضر طويل يمين
      { id: 'p03', file: '03.png', top: 170, left: 220, width: 80, height: 210 },

      // القطعة 4 - مثلث أخضر علوي يمين
      { id: 'p04', file: '04.png', top: 30, left: 220, width: 80, height: 80 },

      // القطعة 5 - مستطيل برتقالي كبير علوي
      { id: 'p05', file: '05.png', top: 30, left: 20, width: 180, height: 80 },

      // القطعة 6 - مستطيل برتقالي طويل أيسر
      { id: 'p06', file: '06.png', top: 170, left: 20, width: 80, height: 210 },

      // القطعة 7 - مستطيل أخضر سفلي وسط
      { id: 'p07', file: '07.png', top: 300, left: 120, width: 80, height: 80 }
    ];

    const container = document.getElementById('logo');

    parts.forEach(p => {
      const img = document.createElement('img');
      img.src = `partsOfLogo/${p.file}`;
      img.id = p.id;
      img.className = 'logo-part';
      img.style.top = `${p.top}px`;
      img.style.left = `${p.left}px`;
      img.style.width = `${p.width}px`;
      img.style.height = `${p.height}px`;
      container.appendChild(img);
    });
  </script>

</body>
</html>
