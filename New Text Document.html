<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <title>Logo Reassembly</title>
  <style>
    body {
      margin: 0;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #f0f0f0;
    }

    .logo-container {
      position: relative;
      width: 650px;
      height: 560px;
      background: white;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
      border-radius: 10px;
    }

    .logo-part {
      position: absolute;
    }
  </style>
</head>
<body>

  <div id="logo" class="logo-container"></div>

  <script>
    // ترتيب القطع حسب الشكل في الصورة المرفقة
    const parts = [
      // الصف العلوي - الجانب الأيسر (برتقالي كبير)
      { id: 'p05', file: '05.png', top: 50, left: 50, width: 280, height: 140 },

      // الصف العلوي - الجانب الأيمن (أخضر مثلث)
      { id: 'p04', file: '04.png', top: 50, left: 450, width: 140, height: 140 },

      // الصف الأوسط - اليسار (برتقالي)
      { id: 'p06', file: '06.png', top: 210, left: 50, width: 130, height: 140 },

      // الصف الأوسط - الوسط (برتقالي)
      { id: 'p02', file: '02.png', top: 210, left: 200, width: 130, height: 140 },

      // الصف الأوسط - اليمين (أخضر طويل)
      { id: 'p03', file: '03.png', top: 210, left: 450, width: 140, height: 280 },

      // الصف السفلي - اليسار (أخضر مثلث)
      { id: 'p01', file: '01.png', top: 370, left: 50, width: 130, height: 140 },

      // الصف السفلي - الوسط (أخضر)
      { id: 'p07', file: '07.png', top: 370, left: 200, width: 230, height: 140 }
    ];

    const container = document.getElementById('logo');

    parts.forEach(p => {
      const img = document.createElement('img');
      img.src = `partsOfLogo/${p.file}`;
      img.id = p.id;
      img.className = 'logo-part';
      img.style.top = `${p.top}px`;
      img.style.left = `${p.left}px`;
      img.style.width = `${p.width}px`;
      img.style.height = `${p.height}px`;
      container.appendChild(img);
    });
  </script>

</body>
</html>
