<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <title>Logo Reassembly</title>
  <style>
    body {
      margin: 0;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #f0f0f0;
    }

    .logo-container {
      position: relative;
      width: 650px;
      height: 560px;
      background: white;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
      border-radius: 10px;
    }

    .logo-part {
      position: absolute;
      border-radius: 8px;
      object-fit: contain;
    }
  </style>
</head>
<body>

  <div id="logo" class="logo-container"></div>

  <script>
    // ترتيب القطع حسب الترقيم من 1 إلى 7 في شكل مربع
    const parts = [
      // القطعة 1 - السفلي الأيسر (أخضر مثلث)
      { id: 'p01', file: '01.png', top: 320, left: 50, width: 120, height: 120 },

      // القطعة 2 - الوسط الأيسر (برتقالي)
      { id: 'p02', file: '02.png', top: 180, left: 190, width: 120, height: 120 },

      // القطعة 3 - اليمين الطويل (أخضر)
      { id: 'p03', file: '03.png', top: 180, left: 330, width: 120, height: 260 },

      // القطعة 4 - العلوي الأيمن (أخضر مثلث)
      { id: 'p04', file: '04.png', top: 40, left: 330, width: 120, height: 120 },

      // القطعة 5 - العلوي الأيسر الكبير (برتقالي)
      { id: 'p05', file: '05.png', top: 40, left: 50, width: 260, height: 120 },

      // القطعة 6 - الأيسر الطويل (برتقالي)
      { id: 'p06', file: '06.png', top: 180, left: 50, width: 120, height: 260 },

      // القطعة 7 - السفلي الأوسط (أخضر)
      { id: 'p07', file: '07.png', top: 320, left: 190, width: 120, height: 120 }
    ];

    const container = document.getElementById('logo');

    parts.forEach(p => {
      const img = document.createElement('img');
      img.src = `partsOfLogo/${p.file}`;
      img.id = p.id;
      img.className = 'logo-part';
      img.style.top = `${p.top}px`;
      img.style.left = `${p.left}px`;
      img.style.width = `${p.width}px`;
      img.style.height = `${p.height}px`;
      container.appendChild(img);
    });
  </script>

</body>
</html>
